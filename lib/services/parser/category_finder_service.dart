import 'dart:convert';
import '../../models/transaction_model.dart';
import '../../models/category_suggestion.dart';
import '../storage_service.dart';
import 'learned_category_storage.dart';
import 'learned_association_service.dart';
import 'category_keyword_map.dart';

/// Service for finding transaction categories using keyword matching and learned associations
class CategoryFinderService {
  final LearnedCategoryStorage _learnedStorage;
  LearnedAssociationService? _learnedAssociationService;

  CategoryFinderService(StorageService storageService)
      : _learnedStorage = LearnedCategoryStorage(storageService) {
    _initializeLearnedAssociationService(storageService);
  }

  /// Initialize the learned association service
  void _initializeLearnedAssociationService(StorageService storageService) async {
    try {
      _learnedAssociationService = await LearnedAssociationService.getInstance(storageService);
    } catch (e) {
      print('Failed to initialize learned association service in CategoryFinderService: $e');
    }
  }

  /// Find category for the given text and transaction type
  /// Returns null if no category can be determined (triggering user selection)
  Future<String?> findCategory(String remainingText, TransactionType type) async {
    if (remainingText.trim().isEmpty) return null;

    // First check new unified learned associations
    if (_learnedAssociationService != null) {
      final learnedAssociation = await _learnedAssociationService!.getAssociation(remainingText);
      if (learnedAssociation?.categoryId != null) {
        return learnedAssociation!.categoryId;
      }
    }

    // Fall back to legacy learned storage for backward compatibility
    final learnedCategory = await _learnedStorage.getLearnedCategory(remainingText);
    if (learnedCategory != null) {
      return learnedCategory;
    }

    // Fall back to keyword matching
    final keywordCategory = findCategoryByKeywords(remainingText);
    if (keywordCategory != null) {
      return keywordCategory;
    }

    // No category found
    return null;
  }

  /// Save a user's category selection for future learning
  Future<void> learnCategory(String text, String categoryId) async {
    // Use new unified service if available
    if (_learnedAssociationService != null) {
      await _learnedAssociationService!.learn(text, categoryId: categoryId);
    } else {
      // Fall back to legacy storage for backward compatibility
      await _learnedStorage.saveLearnedCategory(text, categoryId);
    }
  }

  /// Get all learned associations for debugging
  Future<Map<String, String>> getAllLearnedCategories() async {
    final result = <String, String>{};

    // Get from new service if available
    if (_learnedAssociationService != null) {
      final associations = await _learnedAssociationService!.getAllAssociations();
      for (final entry in associations.entries) {
        if (entry.value.categoryId != null) {
          result[entry.key] = entry.value.categoryId!;
        }
      }
    }

    // Also get from legacy storage for backward compatibility
    // Only add legacy entries if the key doesn't already exist (new service data takes precedence)
    final legacyData = await _learnedStorage.getAllLearnedCategories();
    for (final entry in legacyData.entries) {
      if (!result.containsKey(entry.key)) {
        result[entry.key] = entry.value;
      }
    }

    return result;
  }

  /// Clear all learned data
  Future<void> clearLearnedData() async {
    // Clear new unified service if available
    if (_learnedAssociationService != null) {
      await _learnedAssociationService!.clearAllData();
    }
    // Also clear legacy storage for backward compatibility
    await _learnedStorage.clearLearnedData();
  }

  /// Export learned data for debugging
  Future<String?> exportLearnedData() async {
    final result = <String, dynamic>{};

    // Get from new service if available
    if (_learnedAssociationService != null) {
      final associations = await _learnedAssociationService!.getAllAssociations();
      if (associations.isNotEmpty) {
        result['new_service'] = associations.map((key, value) => MapEntry(key, value.toJson()));
      }
    }

    // Also get from legacy storage for backward compatibility
    final legacyData = await _learnedStorage.exportLearnedData();
    if (legacyData != null) {
      result['legacy_storage'] = legacyData;
    }

    return result.isNotEmpty ? jsonEncode(result) : null;
  }

  /// Get available category IDs for keyword matching
  List<String> getAvailableCategoryIds() {
    return getAllCategoryIds();
  }

  /// Get keywords for a specific category
  List<String> getKeywordsForCategory(String categoryId) {
    return categoryKeywords[categoryId] ?? [];
  }

  /// Find multiple category suggestions with confidence scoring
  /// Returns a ranked list of up to 3 category suggestions
  Future<CategorySuggestionResult> findCategorySuggestions(String remainingText, TransactionType type) async {
    if (remainingText.trim().isEmpty) {
      return CategorySuggestionResult(suggestions: []);
    }

    final suggestions = <CategorySuggestion>[];

    // 1. Check learned associations (highest priority)
    await _addLearnedSuggestions(remainingText, suggestions);

    // 2. Check keyword matching
    _addKeywordSuggestions(remainingText, suggestions);

    // 3. Remove duplicates and sort by confidence
    final uniqueSuggestions = _deduplicateAndSort(suggestions);

    // 4. Take top 3 suggestions
    final topSuggestions = uniqueSuggestions.take(3).toList();

    return CategorySuggestionResult(
      suggestions: topSuggestions,
      fallbackCategoryId: topSuggestions.isNotEmpty ? topSuggestions.first.categoryId : null,
    );
  }

  /// Add suggestions from learned associations
  Future<void> _addLearnedSuggestions(String text, List<CategorySuggestion> suggestions) async {
    if (_learnedAssociationService != null) {
      final learnedAssociation = await _learnedAssociationService!.getAssociation(text);
      if (learnedAssociation?.categoryId != null) {
        // High confidence for learned associations, boosted by confidence count
        final baseConfidence = 0.85;
        final confidenceBoost = (learnedAssociation!.confidence - 1) * 0.02; // +2% per additional confirmation
        final finalConfidence = (baseConfidence + confidenceBoost).clamp(0.0, 1.0);

        suggestions.add(CategorySuggestion(
          categoryId: learnedAssociation.categoryId!,
          confidence: finalConfidence,
          matchReason: 'Previously learned from similar transactions (${learnedAssociation.confidence}x confirmed)',
          source: CategorySuggestionSource.learned,
        ));
      }
    }

    // Also check legacy storage for backward compatibility
    final learnedCategory = await _learnedStorage.getLearnedCategory(text);
    if (learnedCategory != null) {
      // Check if we already have this category from the new service
      final alreadyExists = suggestions.any((s) => s.categoryId == learnedCategory);
      if (!alreadyExists) {
        suggestions.add(CategorySuggestion(
          categoryId: learnedCategory,
          confidence: 0.80, // Slightly lower than new service
          matchReason: 'Previously learned from similar transactions',
          source: CategorySuggestionSource.learned,
        ));
      }
    }
  }

  /// Add suggestions from keyword matching
  void _addKeywordSuggestions(String text, List<CategorySuggestion> suggestions) {
    final keywordMatches = <String, double>{};

    // Get scores for all categories
    for (final categoryId in getAllCategoryIds()) {
      final score = calculateCategoryScore(text, categoryId);
      if (score > 0) {
        keywordMatches[categoryId] = score;
      }
    }

    // Convert to suggestions with normalized confidence scores
    for (final entry in keywordMatches.entries) {
      final categoryId = entry.key;
      final rawScore = entry.value;

      // Normalize score to 0.0-0.7 range (leaving room for learned associations)
      final normalizedConfidence = (rawScore / 20.0).clamp(0.0, 0.7);

      if (normalizedConfidence >= 0.1) { // Only include meaningful matches
        final matchedKeywords = _getMatchedKeywords(text, categoryId);
        final matchReason = matchedKeywords.isNotEmpty
            ? 'Matched keywords: ${matchedKeywords.take(3).join(', ')}'
            : 'Keyword pattern match';

        suggestions.add(CategorySuggestion(
          categoryId: categoryId,
          confidence: normalizedConfidence,
          matchReason: matchReason,
          source: CategorySuggestionSource.keyword,
        ));
      }
    }
  }

  /// Get matched keywords for a category and text
  List<String> _getMatchedKeywords(String text, String categoryId) {
    final keywords = getKeywordsForCategory(categoryId);
    final textLower = text.toLowerCase();
    final words = textLower.split(RegExp(r'\s+'));

    final matched = <String>[];
    for (final keyword in keywords) {
      final keywordLower = keyword.toLowerCase();
      if (words.contains(keywordLower) || textLower.contains(keywordLower)) {
        matched.add(keyword);
      }
    }

    return matched;
  }

  /// Remove duplicate suggestions and sort by confidence
  List<CategorySuggestion> _deduplicateAndSort(List<CategorySuggestion> suggestions) {
    final categoryMap = <String, CategorySuggestion>{};

    // Keep the highest confidence suggestion for each category
    for (final suggestion in suggestions) {
      final existing = categoryMap[suggestion.categoryId];
      if (existing == null || suggestion.confidence > existing.confidence) {
        categoryMap[suggestion.categoryId] = suggestion;
      }
    }

    // Sort by confidence (highest first)
    final result = categoryMap.values.toList();
    result.sort((a, b) => b.confidence.compareTo(a.confidence));

    return result;
  }
}
