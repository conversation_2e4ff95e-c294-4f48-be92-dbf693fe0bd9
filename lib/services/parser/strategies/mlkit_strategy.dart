import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../../../models/transaction_model.dart';
import '../../../models/parse_result.dart';
import '../../../models/amount_candidate.dart';
import '../../../utils/currency_utils.dart';
import '../../../utils/amount_utils.dart';
import '../../../utils/raw_number_finder.dart';
import '../../storage_service.dart';
import '../category_finder_service.dart';
import '../entity_extractor_base.dart';
import '../parsing_strategy.dart';
import '../parsing_context.dart';
import '../parsing_config.dart';

/// Strategy that handles ML Kit parsing with Trust but Verify approach.
/// 
/// This strategy implements the complete ML Kit parsing logic including:
/// - ML Kit entity extraction (if available)
/// - Independent RawNumberFinder processing
/// - Candidate consolidation
/// - Ambiguity detection
/// - Best candidate selection
class MlKitStrategy implements ParsingStrategy {
  final EntityExtractorBase? _entityExtractor;
  final StorageService _storageService;
  final CategoryFinderService _categoryFinder;
  final Uuid _uuid;
  final bool _mlKitAvailable;
  final ParsingConfig _config;

  /// Creates a new MlKitStrategy with required dependencies.
  ///
  /// [entityExtractor] - Optional ML Kit entity extractor (can be null)
  /// [storageService] - Service for accessing storage
  /// [categoryFinder] - Service for finding transaction categories
  /// [uuid] - UUID generator for creating transaction IDs
  /// [mlKitAvailable] - Flag indicating if ML Kit should be used
  /// [config] - Parsing configuration for customizable behavior
  MlKitStrategy(
    this._entityExtractor,
    this._storageService,
    this._categoryFinder,
    this._uuid,
    this._mlKitAvailable,
    this._config,
  );

  @override
  String get name => 'MlKitStrategy';

  @override
  Future<ParseResult?> execute(ParsingContext context) async {
    try {
      return await _parseWithMLKit(context.text);
    } catch (e) {
      print('Error in MlKitStrategy: $e');
      return null; // Decline to handle on error
    }
  }

  /// Main ML Kit parsing logic
  /// 
  /// This method is copied from MlKitParserService._parseWithMLKit
  /// to implement the complete Trust but Verify approach.
  Future<ParseResult> _parseWithMLKit(String text) async {
    print('MLKIT_DEBUG: _parseWithMLKit called with text: "$text"');

    // Step 1: Extract entities using ML Kit (if available)
    List<EntityAnnotationBase> entities = [];
    if (_mlKitAvailable && _entityExtractor != null && _entityExtractor!.isInitialized) {
      try {
        entities = await _entityExtractor!.annotateText(text);
        print('MLKIT_DEBUG: ML Kit found ${entities.length} entities: ${entities.map((e) => '${e.entityType}:${e.text}').toList()}');
      } catch (e) {
        print('MLKIT_DEBUG: ML Kit extraction failed: $e, continuing with raw number finder only');
        // Continue with empty entities list - we'll still use raw number finder
      }
    } else {
      print('MLKIT_DEBUG: ML Kit entity extraction not available, using raw number finder only');
    }

    // Step 1: Convert ML Kit entities to AmountCandidate objects
    final List<AmountCandidate> mlKitCandidates = [];
    DateTime? extractedDate;
    String remainingText = text;

    for (final entity in entities) {
      if (entity.entityType == EntityType.money) {
        debugPrint('DEBUG: Processing ML Kit money entity: "${entity.text}"');

        // Parse the money entity (without filtering embedded numbers)
        final result = _parseMoneyEntityToCandidate(entity, text);
        if (result != null) {
          mlKitCandidates.add(result);
          debugPrint('DEBUG: Added ML Kit candidate: ${result.amount} (${result.currency})');
        } else {
          debugPrint('DEBUG: Failed to parse ML Kit money entity: "${entity.text}"');
        }
        remainingText = _removeEntityFromText(remainingText, entity);
      } else if (entity.entityType == EntityType.dateTime) {
        extractedDate = _parseDateTimeEntity(entity);
        remainingText = _removeEntityFromText(remainingText, entity);
        debugPrint('DEBUG: Extracted date: $extractedDate');
      }
    }

    debugPrint('DEBUG: ML Kit candidates: ${mlKitCandidates.map((c) => c.amount).toList()}');

    // Step 2: Independently run raw number finder on original text
    final List<AmountCandidate> rawCandidates = RawNumberFinder.findAllNumbers(text);
    print('MLKIT_DEBUG: Raw finder found ${rawCandidates.length} candidates: ${rawCandidates.map((c) => c.amount).toList()}');

    // Step 3: Consolidate both lists into comprehensive candidates
    final List<AmountCandidate> consolidatedCandidates = _consolidateCandidates(mlKitCandidates, rawCandidates);
    print('MLKIT_DEBUG: Consolidated candidates: ${consolidatedCandidates.map((c) => c.amount).toList()}');

    // Step 4: Apply ambiguity detection to consolidated list
    final ambiguityResult = await _detectAmountAmbiguityFromCandidates(consolidatedCandidates, text);
    if (ambiguityResult != null) {
      print('MLKIT_DEBUG: Ambiguity detected, triggering amount confirmation');
      return ambiguityResult;
    }

    print('MLKIT_DEBUG: No ambiguity detected, proceeding with best candidate selection');

    // If no ambiguity detected, select the best candidate
    final selectedCandidate = _selectBestAmountFromCandidates(consolidatedCandidates, text);
    if (selectedCandidate == null) {
      debugPrint('No valid amount candidate found, returning missingAmount');
      final fallbackTransaction = await _createFallbackTransaction(text);
      return ParseResult.missingAmount(
        fallbackTransaction,
        ambiguityType: AmbiguityType.missingAmount,
      );
    }

    double finalAmount = selectedCandidate.amount;
    String? finalCurrency = selectedCandidate.currency;

    // If no currency found, get default currency
    if (finalCurrency == null) {
      finalCurrency = await _storageService.getDefaultCurrency();
    }

    // Detect transaction type with negative amount information
    final isNegativeAmount = text.trim().startsWith('-');
    final type = _detectTransactionType(text, isNegativeAmount: isNegativeAmount);

    // If transaction type is unclear, return needsType status
    if (type == null) {
      // Create partial transaction with default expense type for type disambiguation
      final partialTransaction = Transaction(
        id: _uuid.v4(),
        amount: finalAmount,
        type: TransactionType.expense, // Default type, will be updated by user selection
        categoryId: 'unknown',
        date: extractedDate ?? DateTime.now(),
        description: _createDescription(text),
        tags: _extractTags(text),
        currencyCode: finalCurrency,
      );

      return ParseResult.needsType(partialTransaction, ambiguityType: AmbiguityType.ambiguousType);
    }

    // Find category using the category finder service
    final categoryId = await _categoryFinder.findCategory(remainingText, type);

    // Create the transaction - use 'unknown' placeholder when no category found
    final transaction = Transaction(
      id: _uuid.v4(),
      amount: finalAmount,
      type: type,
      categoryId: categoryId ?? 'unknown', // Use placeholder for unknown categories
      date: extractedDate ?? DateTime.now(),
      description: _createDescription(text),
      tags: _extractTags(text),
      currencyCode: finalCurrency,
    );

    // Return result indicating if category selection is needed
    if (categoryId == null) {
      return ParseResult.needsCategory(transaction, ambiguityType: AmbiguityType.ambiguousCategory);
    } else {
      return ParseResult.success(transaction);
    }
  }

  /// Parse ML Kit money entity to AmountCandidate
  ///
  /// This method is copied from MlKitParserService._parseMoneyEntityToCandidate
  AmountCandidate? _parseMoneyEntityToCandidate(EntityAnnotationBase entity, String fullText) {
    try {
      // Extract the numeric value and currency from the money entity text
      final entityText = entity.text;

      // Enhanced regex pattern to include abbreviations using configurable pattern
      final numericRegex = RegExp(r'(\d+(?:,\d{3})*(?:\.\d+)?' + _config.abbreviationPattern + r'?|\d+\.\d+' + _config.abbreviationPattern + r'?)');
      final match = numericRegex.firstMatch(entityText);
      double? amount;
      String? currency;

      if (match != null) {
        final numericString = match.group(1)!.replaceAll(',', '');

        // Use AmountUtils.parseAbbreviatedNumber() when abbreviation suffixes are detected
        if (numericString.toLowerCase().contains(RegExp(_config.abbreviationPattern.toLowerCase() + r'$'))) {
          amount = AmountUtils.parseAbbreviatedNumber(numericString);
        } else {
          amount = double.tryParse(numericString);
        }
      }

      // Try to extract currency from the text with context
      currency = _extractCurrencyFromText(entityText);

      if (amount != null) {
        return AmountCandidate.fromMLKit(
          amount: amount,
          currency: currency,
          start: entity.start,
          end: entity.end,
          sourceText: entityText,
        );
      }
    } catch (e) {
      // Fallback to text parsing - handle thousands separators properly
      final cleanText = entity.text.replaceAll(RegExp(r'[^\d.,]'), '');
      // Remove thousands separators (commas) but preserve decimal point
      final normalizedText = cleanText.replaceAll(',', '');
      final amount = double.tryParse(normalizedText);
      if (amount != null) {
        return AmountCandidate.fromMLKit(
          amount: amount,
          currency: _extractCurrencyFromText(entity.text),
          start: entity.start,
          end: entity.end,
          sourceText: entity.text,
        );
      }
    }
    return null;
  }

  /// Consolidate ML Kit and raw number finder candidates
  ///
  /// This method is copied from MlKitParserService._consolidateCandidates
  List<AmountCandidate> _consolidateCandidates(
    List<AmountCandidate> mlKitCandidates,
    List<AmountCandidate> rawCandidates,
  ) {
    debugPrint('DEBUG: _consolidateCandidates called');
    debugPrint('DEBUG: ML Kit candidates: ${mlKitCandidates.map((c) => c.amount).toList()}');
    debugPrint('DEBUG: Raw candidates: ${rawCandidates.map((c) => c.amount).toList()}');

    final List<AmountCandidate> consolidated = [];

    // Add all ML Kit candidates first
    consolidated.addAll(mlKitCandidates);
    debugPrint('DEBUG: Added ${mlKitCandidates.length} ML Kit candidates to consolidated list');

    // Add raw candidates that don't duplicate ML Kit candidates
    for (final rawCandidate in rawCandidates) {
      bool isDuplicate = false;

      for (final mlKitCandidate in mlKitCandidates) {
        // Check if they represent the same amount and position
        if (mlKitCandidate == rawCandidate) {
          isDuplicate = true;
          debugPrint('DEBUG: Raw candidate ${rawCandidate.amount} is duplicate of ML Kit candidate');
          break;
        }
      }

      if (!isDuplicate) {
        consolidated.add(rawCandidate);
        debugPrint('DEBUG: Added unique raw candidate: ${rawCandidate.amount}');
      }
    }

    // Sort by position for consistent ordering
    consolidated.sort((a, b) => a.start.compareTo(b.start));

    debugPrint('DEBUG: Final consolidated list: ${consolidated.map((c) => c.amount).toList()}');
    return consolidated;
  }

  /// Detect amount ambiguity from candidates
  ///
  /// This method is copied from MlKitParserService._detectAmountAmbiguityFromCandidates
  Future<ParseResult?> _detectAmountAmbiguityFromCandidates(
    List<AmountCandidate> candidates,
    String text,
  ) async {
    print('MLKIT_DEBUG: _detectAmountAmbiguityFromCandidates called with ${candidates.length} candidates');
    print('MLKIT_DEBUG: Total candidates: ${candidates.map((c) => c.amount).toList()}');

    // Check for missing amount case
    if (candidates.isEmpty) {
      debugPrint('DEBUG: No valid amount candidates found - returning missingAmount');
      final fallbackTransaction = await _createFallbackTransaction(text);
      return ParseResult.missingAmount(
        fallbackTransaction,
        ambiguityType: AmbiguityType.missingAmount,
      );
    }

    if (candidates.length == 1) {
      debugPrint('DEBUG: No ambiguity - only 1 candidate');
      return null; // No ambiguity with 1 candidate
    }

    // Filter out embedded candidates first
    final nonEmbeddedCandidates = candidates.where((c) => !_isEmbeddedInVendorName(c, text)).toList();
    debugPrint('DEBUG: Filtered out embedded candidates, remaining: ${nonEmbeddedCandidates.map((c) => c.amount).toList()}');

    // If filtering left us with only one candidate, no ambiguity
    if (nonEmbeddedCandidates.length <= 1) {
      debugPrint('DEBUG: After filtering embedded numbers, only ${nonEmbeddedCandidates.length} candidates remain - no ambiguity');
      return null;
    }

    // Use non-embedded candidates for ambiguity detection, or all if none are non-embedded
    final candidatesToCheck = nonEmbeddedCandidates.isNotEmpty ? nonEmbeddedCandidates : candidates;

    // Remove duplicates based on amount to get unique amounts
    final uniqueCandidates = <AmountCandidate>[];
    for (final candidate in candidatesToCheck) {
      bool isDuplicate = false;
      for (final existing in uniqueCandidates) {
        if (existing.hasSameAmount(candidate)) {
          isDuplicate = true;
          debugPrint('DEBUG: Duplicate amount found: ${candidate.amount} (skipping)');
          break;
        }
      }
      if (!isDuplicate) {
        uniqueCandidates.add(candidate);
        debugPrint('DEBUG: Added unique candidate: ${candidate.amount}');
      }
    }

    debugPrint('DEBUG: Unique candidates after deduplication: ${uniqueCandidates.map((c) => c.amount).toList()}');

    // Simple rule: If there are multiple unique amounts, it's ambiguous
    if (uniqueCandidates.length > 1) {
      print('MLKIT_DEBUG: Multiple unique amounts detected - triggering ambiguous amount');
      print('MLKIT_DEBUG: Final decision: AMBIGUOUS AMOUNT DETECTED');

      final amounts = uniqueCandidates.map((c) => c.amount).toList();
      final texts = uniqueCandidates.map((c) => _formatAmountForDisplay(c.amount)).toList();

      // Get currency from candidates or extract from text
      String currency = await _storageService.getDefaultCurrency(); // Use user's default currency

      // First try to get currency from candidates
      for (final candidate in uniqueCandidates) {
        if (candidate.currency != null) {
          currency = candidate.currency!;
          break;
        }
      }

      // If no candidate has currency, extract from full text
      final extractedCurrency = _extractCurrencyFromText(text);
      if (extractedCurrency != null) {
        currency = extractedCurrency;
      }

      // Select the best amount for the partial transaction
      final bestCandidate = _selectBestAmountFromCandidates(uniqueCandidates, text);
      final bestAmount = bestCandidate?.amount ?? amounts.first;

      // Create partial transaction for confirmation
      final partialTransaction = Transaction(
        id: _uuid.v4(),
        amount: bestAmount,
        type: _detectTransactionType(text) ?? TransactionType.expense,
        categoryId: 'unknown',
        date: DateTime.now(),
        description: text.trim(),
        tags: _extractTags(text),
        currencyCode: currency,
      );

      return ParseResult.needsAmountConfirmation(
        partialTransaction,
        amounts,
        texts,
        ambiguityType: AmbiguityType.ambiguousAmount,
      );
    }

    debugPrint('DEBUG: Only one unique amount found - no ambiguity');
    return null; // No ambiguity detected
  }

  /// Select best amount from candidates
  ///
  /// This method is copied from MlKitParserService._selectBestAmountFromCandidates
  AmountCandidate? _selectBestAmountFromCandidates(List<AmountCandidate> candidates, String text) {
    if (candidates.isEmpty) return null;
    if (candidates.length == 1) return candidates.first;

    // Prefer non-embedded amounts
    final nonEmbedded = candidates.where((c) => !_isEmbeddedInVendorName(c, text)).toList();
    if (nonEmbedded.isNotEmpty) {
      candidates = nonEmbedded;
    }

    // Prefer amounts with abbreviations (k, m, b)
    final withAbbreviations = candidates.where((c) => _hasAbbreviation(c.sourceText)).toList();
    if (withAbbreviations.isNotEmpty) {
      candidates = withAbbreviations;
    }

    // Prefer amounts with currency information
    final withCurrency = candidates.where((c) => c.currency != null).toList();
    if (withCurrency.isNotEmpty) {
      return withCurrency.first;
    }

    return candidates.first;
  }

  /// Check if amount is embedded in vendor name
  ///
  /// This method is copied from MlKitParserService._isEmbeddedInVendorName
  bool _isEmbeddedInVendorName(AmountCandidate candidate, String text) {
    final beforeText = text.substring(0, candidate.start);
    final afterText = text.substring(candidate.end);

    final beforeLetters = _countSurroundingLetters(beforeText, false);
    final afterLetters = _countSurroundingLetters(afterText, true);

    // If surrounded by letters on at least one side with sufficient context, likely embedded
    // Examples: "lux68" (3 letters before), "hotel789" (5 letters before), "cafe123abc" (3 before, 3 after)
    return (beforeLetters >= _config.strictEmbeddedLetterThreshold && afterLetters >= 0) ||
           (beforeLetters >= 0 && afterLetters >= _config.strictEmbeddedLetterThreshold) ||
           (beforeLetters >= _config.embeddedLetterThreshold && afterLetters >= _config.embeddedLetterThreshold);
  }

  /// Check if text has abbreviation
  ///
  /// This method is copied from MlKitParserService._hasAbbreviation
  bool _hasAbbreviation(String text) {
    return RegExp(r'\d+' + _config.abbreviationPattern).hasMatch(text);
  }

  /// Count surrounding letters
  ///
  /// This method is copied from MlKitParserService._countSurroundingLetters
  int _countSurroundingLetters(String text, bool fromStart) {
    if (text.isEmpty) return 0;

    int count = 0;
    final chars = fromStart ? text.split('') : text.split('').reversed.toList();

    for (final char in chars) {
      if (RegExp(r'[a-zA-Z]').hasMatch(char)) {
        count++;
      } else {
        break;
      }
    }

    return count;
  }

  /// Format amount for display
  ///
  /// This method is copied from MlKitParserService._formatAmountForDisplay
  String _formatAmountForDisplay(double amount) {
    if (amount == amount.toInt()) {
      return amount.toInt().toString();
    } else {
      return amount.toStringAsFixed(2);
    }
  }

  /// Parse datetime entity
  ///
  /// This method is copied from MlKitParserService._parseDateTimeEntity
  DateTime? _parseDateTimeEntity(EntityAnnotationBase entity) {
    // Simple implementation - could be enhanced
    return DateTime.now();
  }

  /// Remove entity from text
  ///
  /// This method is copied from MlKitParserService._removeEntityFromText
  String _removeEntityFromText(String text, EntityAnnotationBase entity) {
    if (entity.start >= 0 && entity.end <= text.length) {
      return text.substring(0, entity.start) + text.substring(entity.end);
    }
    return text;
  }

  /// Extract currency from text
  ///
  /// This method is copied from MlKitParserService._extractCurrencyFromText
  String? _extractCurrencyFromText(String text) {
    // Check for currency symbols first with context-aware detection
    final symbolRegex = RegExp(r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)');
    final symbolMatch = symbolRegex.firstMatch(text);
    if (symbolMatch != null) {
      return CurrencyUtils.symbolToCurrencyCode(symbolMatch.group(1)!, context: text);
    }

    // Check for currency codes
    final codeRegex = RegExp(r'\b(USD|EUR|GBP|JPY|CNY|INR|KRW|MXN|PHP|VND|THB|TRY|ILS|BRL|SGD|HKD|AUD|CAD|NZD|RUB)\b', caseSensitive: false);
    final codeMatch = codeRegex.firstMatch(text);
    if (codeMatch != null) {
      return codeMatch.group(1)!.toUpperCase();
    }

    // Check for currency names
    final nameMap = {
      'dollars?': 'USD',
      'euros?': 'EUR',
      'pounds?': 'GBP',
      'yen': 'JPY',
      'yuan': 'CNY',
      'rupees?': 'INR',
      'won': 'KRW',
      'pesos?': 'MXN',
      'dong': 'VND',
      'baht': 'THB',
      'lira': 'TRY',
      'shekel': 'ILS',
      'reais?': 'BRL',
      'rubles?': 'RUB',
    };

    for (final entry in nameMap.entries) {
      if (RegExp(entry.key, caseSensitive: false).hasMatch(text)) {
        return entry.value;
      }
    }

    return null;
  }

  /// Detect transaction type
  ///
  /// This method is copied from MlKitParserService._detectTransactionType
  TransactionType? _detectTransactionType(String text, {bool isNegativeAmount = false}) {
    final lowerText = text.toLowerCase();

    // Check for explicit income keywords
    if (RegExp(r'\b(income|salary|wage|bonus|refund|cashback|dividend|interest|received|deposit)\b').hasMatch(lowerText)) {
      return TransactionType.income;
    }

    // Check for explicit loan keywords
    if (RegExp(r'\b(loan|borrow\w*|lend\w*|debt|credit)\b').hasMatch(lowerText)) {
      return TransactionType.loan;
    }

    // Check for expense keywords or negative amounts
    if (RegExp(r'\b(spent|paid|bought|purchase|expense|cost)\b').hasMatch(lowerText) || isNegativeAmount) {
      return TransactionType.expense;
    }

    // Default to null for ambiguous cases
    return null;
  }

  /// Create description from text
  ///
  /// This method is copied from MlKitParserService._createDescription
  String _createDescription(String text) {
    return text.trim();
  }

  /// Extract hashtags from text
  ///
  /// This method is copied from MlKitParserService._extractTags
  List<String> _extractTags(String text) {
    final tags = <String>[];
    final hashtagRegex = RegExp(r'#(\w+)');
    final matches = hashtagRegex.allMatches(text);

    for (final match in matches) {
      final tag = match.group(1);
      if (tag != null) {
        tags.add(tag);
      }
    }

    return tags;
  }

  /// Create fallback transaction
  ///
  /// This method is copied from MlKitParserService._createFallbackTransaction
  Future<Transaction> _createFallbackTransaction(String text) async {
    // Use configuration default currency as fallback, but still check storage for user preference
    final defaultCurrency = await _storageService.getDefaultCurrency();

    return Transaction(
      id: _uuid.v4(),
      amount: 0.0,
      type: TransactionType.expense,
      categoryId: 'unknown',
      date: DateTime.now(),
      description: text.trim(),
      tags: _extractTags(text),
      currencyCode: defaultCurrency,
    );
  }
}
