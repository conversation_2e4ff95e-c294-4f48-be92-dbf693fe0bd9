import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';

import '../models/transaction_model.dart';
import '../models/parse_result.dart';
import '../services/parser/transaction_parsing_service.dart';
import '../services/parser/learned_association_service.dart';
import '../services/parser/category_keyword_map.dart';
import '../widgets/transaction_message.dart';
import '../widgets/transaction_edit_dialog.dart';
import '../widgets/category_picker_dialog.dart';
import '../widgets/quick_reply_widget.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  TransactionParsingService? _parserService;
  LearnedAssociationService? _learnedAssociationService;
  final Uuid _uuid = Uuid();
  late AnimationController _animationController;
  bool _initialLoading = true;

  // State management for pending selections
  ParseResult? _pendingTypeSelection;
  ParseResult? _pendingCategorySelection;
  String? _pendingOriginalText;

  // State management for pending amount confirmation
  ParseResult? _pendingAmountConfirmation;

  // Keep this widget alive when it's not visible
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Initialize ML Kit parser service
    _initializeParserService();

    // Add welcome message if this is the first time
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<TransactionProvider>(context, listen: false);
      
      // Add listener to scroll to bottom when messages change
      provider.addListener(_onProviderChanged);
      
      // Hide the initial loading indicator after a short delay
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          setState(() {
            _initialLoading = false;
          });
          
          // Scroll to bottom after loading is done
          _scrollToBottom();
        }
      });
      
      if (provider.messages.isEmpty) {
        _addSystemMessage('👋 Welcome to Money Lover Chat! Tell me about your financial transactions and I\'ll help you keep track of them.\n\nTry saying something like "Spent \$25 on dinner" or "Got \$1500 salary today".');
      }
      
      // Setup scroll controller to load more messages when scrolling to top
      _scrollController.addListener(_scrollListener);
    });
  }

  void _initializeParserService() async {
    // Get ML Kit service from the provider instead of creating new instances
    final mlKitProvider = Provider.of<ValueNotifier<TransactionParsingService?>>(context, listen: false);

    // Set up listener for when ML Kit service becomes available
    mlKitProvider.addListener(_onMlKitServiceChanged);

    // Use the current service if available
    _parserService = mlKitProvider.value;

    // Initialize learned association service from storage service
    try {
      final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);
      // Access the storage service through the transaction provider
      // This is a temporary solution - ideally we'd have direct access to storage service
      print('Parser service setup completed');

      // Show appropriate message based on ML Kit availability
      if (_parserService == null) {
        _showParsingModeMessage(false);
      } else {
        _showParsingModeMessage(true);
      }
    } catch (e) {
      print('Parser service setup failed: $e');
      _showParsingModeMessage(false);
    }
  }

  void _onMlKitServiceChanged() {
    final mlKitProvider = Provider.of<ValueNotifier<TransactionParsingService?>>(context, listen: false);
    final newService = mlKitProvider.value;

    if (newService != null && _parserService == null) {
      setState(() {
        _parserService = newService;
      });
      _showParsingModeMessage(true);
      print('ML Kit service became available');
    }
  }

  void _showParsingModeMessage(bool mlKitAvailable) {
    if (!mounted) return;

    final message = mlKitAvailable
        ? 'Advanced parsing mode active (ML Kit available)'
        : 'Basic parsing mode active (ML Kit initializing...)';

    final color = mlKitAvailable ? Colors.green : Colors.orange;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _animationController.dispose();

    // Remove provider listeners
    Provider.of<TransactionProvider>(context, listen: false).removeListener(_onProviderChanged);

    // Remove ML Kit service listener
    try {
      Provider.of<ValueNotifier<TransactionParsingService?>>(context, listen: false)
          .removeListener(_onMlKitServiceChanged);
    } on StateError catch (e) {
      // Handle provider-related state errors during disposal
      debugPrint('Expected disposal error when removing ML Kit listener: $e');
    } catch (e) {
      // Log unexpected errors instead of silently ignoring them
      debugPrint('Unexpected error when removing ML Kit listener: $e');
    }

    super.dispose();
  }
  
  void _scrollListener() {
    if (_scrollController.position.pixels <= _scrollController.position.minScrollExtent + 50) {
      final provider = Provider.of<TransactionProvider>(context, listen: false);
      
      // Save current position and content size
      final currentPosition = _scrollController.position.pixels;
      final currentContentSize = _scrollController.position.maxScrollExtent;
      
      provider.loadMoreMessages().then((_) {
        // Wait for layout to update with new content
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_scrollController.hasClients) {
            // Calculate new position to maintain relative scroll position
            final newContentSize = _scrollController.position.maxScrollExtent;
            final newPosition = newContentSize - currentContentSize + currentPosition;
            
            if (newPosition > 0) {
              _scrollController.jumpTo(newPosition);
            }
          }
        });
      });
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    final provider = Provider.of<TransactionProvider>(context, listen: false);

    // Add user message
    _addUserMessage(text);

    // Clear input field
    _messageController.clear();

    // Parse transaction
    if (_parserService != null) {
      try {
        final parseResult = await _parserService!.parseTransaction(text);

        // Handle different parse statuses
        switch (parseResult.status) {
          case ParseStatus.success:
            // Automatically save the transaction
            await provider.addTransactionFromChat(parseResult.transaction, text);
            _scrollToBottom();
            break;

          case ParseStatus.needsCategory:
            // Show enhanced category selection with contextual suggestions
            await _handleCategorySelectionWithQuickReplies(parseResult, text);
            break;

          case ParseStatus.needsType:
            // Show quick reply for transaction type selection
            await _handleTypeSelection(parseResult, text);
            break;

          case ParseStatus.needsAmountConfirmation:
            // Show amount confirmation with quick replies
            print('DEBUG: Amount confirmation triggered for text: "$text"');
            print('DEBUG: candidateAmounts: ${parseResult.candidateAmounts}');
            print('DEBUG: candidateTexts: ${parseResult.candidateTexts}');
            await _handleAmountConfirmation(parseResult, text);
            break;

          case ParseStatus.missingAmount:
            // No amount detected - show contextual helpful message with smart suggestions
            await _handleMissingAmount(parseResult, text);
            break;

          case ParseStatus.ambiguousAmount:
            // Multiple amounts detected - show amount confirmation (same as needsAmountConfirmation)
            print('DEBUG: Ambiguous amount triggered for text: "$text"');
            print('DEBUG: candidateAmounts: ${parseResult.candidateAmounts}');
            print('DEBUG: candidateTexts: ${parseResult.candidateTexts}');
            await _handleAmountConfirmation(parseResult, text);
            break;

          case ParseStatus.failed:
            // Parsing failed - show error message
            _addSystemMessage(
              parseResult.error ??
              "I couldn't detect a transaction in your message. Try using formats like "
              "\"spent \$50 on groceries\" or \"received \$1000 salary\".",
            );
            break;
        }
      } catch (e) {
        _addSystemMessage(
          "Sorry, there was an error processing your message. Please try again.",
        );
      }
    } else {
      // Parser service not initialized
      _addSystemMessage(
        "Parser service is not ready yet. Please try again in a moment.",
      );
    }
  }

  /// Handles missing amount scenario with contextual feedback and smart suggestions
  Future<void> _handleMissingAmount(ParseResult parseResult, String originalText) async {
    // Generate contextual message
    final contextualMessage = _generateContextualMessage(parseResult, originalText);

    // Generate smart amount suggestions based on description
    final amountSuggestions = _getAmountSuggestions(parseResult.transaction.description);

    // Create system message with quick replies for amount suggestions
    final quickReplyId = _uuid.v4();
    final message = ChatMessage.systemWithQuickReplies(
      id: _uuid.v4(),
      text: contextualMessage,
      timestamp: DateTime.now(),
      quickReplies: amountSuggestions,
      quickReplyId: quickReplyId,
    );

    final provider = Provider.of<TransactionProvider>(context, listen: false);
    await provider.addMessage(message);
    _scrollToBottom();
  }

  Future<void> _handleTypeSelection(ParseResult parseResult, String originalText) async {
    // Store pending transaction for type selection
    _pendingTypeSelection = parseResult;
    _pendingOriginalText = originalText;

    // Generate contextual message
    final contextualMessage = parseResult.ambiguityType == AmbiguityType.ambiguousType
        ? _generateContextualMessage(parseResult, originalText)
        : 'I see ${_formatCurrency(parseResult.transaction.amount, parseResult.transaction.currencyCode)} for "${parseResult.transaction.description}". What type of transaction is this?';

    // Create system message with quick replies for transaction type
    final quickReplyId = _uuid.v4();
    final message = ChatMessage.systemWithQuickReplies(
      id: _uuid.v4(),
      text: contextualMessage,
      timestamp: DateTime.now(),
      quickReplies: const ['Expense', 'Income', 'Cancel'],
      quickReplyId: quickReplyId,
    );

    final provider = Provider.of<TransactionProvider>(context, listen: false);
    await provider.addMessage(message);
    _scrollToBottom();
  }

  /// Generates contextual feedback messages based on ambiguity type
  String _generateContextualMessage(ParseResult parseResult, String originalText) {
    final ambiguityType = parseResult.ambiguityType;

    switch (ambiguityType) {
      case AmbiguityType.missingAmount:
        return "I couldn't detect an amount in your message. Try formats like "
               "\"\$50\", \"100 USD\", or \"twenty dollars\". "
               "Examples: \"spent \$25 on coffee\" or \"received 1000 salary\".";

      case AmbiguityType.ambiguousAmount:
        return "I found multiple amounts in your message. Which one represents "
               "the transaction amount? The others might be references, dates, or quantities.";

      case AmbiguityType.ambiguousType:
        return "I couldn't determine if this is an expense or income. "
               "Try using words like \"spent\", \"paid\", \"bought\" for expenses, "
               "or \"received\", \"earned\", \"got\" for income.";

      case AmbiguityType.ambiguousCategory:
        return "I had trouble categorizing this transaction. "
               "The description \"${parseResult.transaction.description}\" could fit multiple categories. "
               "Please help me choose the most appropriate one.";

      default:
        // Fallback to existing generic messages
        switch (parseResult.status) {
          case ParseStatus.missingAmount:
            return "I couldn't detect an amount in your message. Please include an amount like "
                   "\"\$50\" or \"100 USD\" and try again.";
          case ParseStatus.ambiguousAmount:
            return "I found multiple possible amounts in your message. Which amount did you mean?";
          case ParseStatus.needsType:
            return "What type of transaction is this?";
          case ParseStatus.needsCategory:
            return "Please select a category for this transaction.";
          default:
            return "I need more information to process this transaction.";
        }
    }
  }

  /// Generates smart amount suggestions based on transaction description
  List<String> _getAmountSuggestions(String description) {
    final suggestions = <String>[];
    final lowerDesc = description.toLowerCase();

    // Common amount suggestions based on transaction type
    if (lowerDesc.contains('coffee') || lowerDesc.contains('starbucks') || lowerDesc.contains('cafe')) {
      suggestions.addAll(['\$5', '\$10', '\$15']);
    } else if (lowerDesc.contains('lunch') || lowerDesc.contains('dinner') || lowerDesc.contains('restaurant')) {
      suggestions.addAll(['\$15', '\$25', '\$50']);
    } else if (lowerDesc.contains('gas') || lowerDesc.contains('fuel') || lowerDesc.contains('petrol')) {
      suggestions.addAll(['\$30', '\$50', '\$75']);
    } else if (lowerDesc.contains('grocery') || lowerDesc.contains('supermarket') || lowerDesc.contains('food shopping')) {
      suggestions.addAll(['\$25', '\$50', '\$100']);
    } else if (lowerDesc.contains('salary') || lowerDesc.contains('paycheck') || lowerDesc.contains('wage')) {
      suggestions.addAll(['\$1000', '\$2000', '\$3000']);
    } else {
      // Generic suggestions
      suggestions.addAll(['\$10', '\$25', '\$50', '\$100']);
    }

    suggestions.add('Enter Amount');
    return suggestions;
  }

  /// Generates category suggestions based on transaction description and type
  List<String> _getCategorySuggestions(Transaction transaction) {
    final suggestions = <String>[];
    final description = transaction.description.toLowerCase();
    final type = transaction.type;

    // Get categories for the transaction type
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    final availableCategories = provider.categories
        .where((cat) => cat.type == type)
        .toList();

    // Score categories based on description keywords
    final categoryScores = <Category, int>{};

    for (final category in availableCategories) {
      int score = 0;
      final categoryKeywords = _getCategoryKeywords(category.id);

      for (final keyword in categoryKeywords) {
        if (description.contains(keyword.toLowerCase())) {
          score += keyword.length > 5 ? 3 : 2; // Longer keywords get higher scores
        }
      }

      if (score > 0) {
        categoryScores[category] = score;
      }
    }

    // Sort by score and take top suggestions
    final sortedCategories = categoryScores.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // Add top 3 suggestions
    for (int i = 0; i < 3 && i < sortedCategories.length; i++) {
      suggestions.add(sortedCategories[i].key.name);
    }

    // Always add "Other" option
    if (!suggestions.contains('Other')) {
      suggestions.add('Other');
    }

    return suggestions;
  }

  /// Gets keywords for a category using the centralized keyword map
  List<String> _getCategoryKeywords(String categoryId) {
    // Use the centralized keyword map directly
    return categoryKeywords[categoryId] ?? [];
  }

  /// Generates smart quick reply suggestions based on ambiguity type and transaction data
  List<String> _generateSmartQuickReplies(ParseResult parseResult, String originalText) {
    final ambiguityType = parseResult.ambiguityType;

    switch (ambiguityType) {
      case AmbiguityType.missingAmount:
        return _getAmountSuggestions(parseResult.transaction.description);

      case AmbiguityType.ambiguousAmount:
        // For ambiguous amounts, use the existing candidate texts
        final candidateTexts = parseResult.candidateTexts ?? [];
        return [...candidateTexts, 'Cancel'];

      case AmbiguityType.ambiguousType:
        // Enhanced type suggestions with context
        return ['Expense', 'Income', 'Cancel'];

      case AmbiguityType.ambiguousCategory:
        return _getCategorySuggestions(parseResult.transaction);

      default:
        // Fallback to basic options
        return ['Cancel'];
    }
  }



  /// Enhanced category selection handler with quick replies for contextual suggestions
  Future<void> _handleCategorySelectionWithQuickReplies(ParseResult parseResult, String originalText) async {
    // Store pending transaction for category selection
    _pendingCategorySelection = parseResult;
    _pendingOriginalText = originalText;

    final provider = Provider.of<TransactionProvider>(context, listen: false);

    // Generate contextual message and smart suggestions
    final contextualMessage = parseResult.ambiguityType == AmbiguityType.ambiguousCategory
        ? _generateContextualMessage(parseResult, originalText)
        : 'Please select a category for this transaction:';

    final smartSuggestions = parseResult.ambiguityType == AmbiguityType.ambiguousCategory
        ? _getCategorySuggestions(parseResult.transaction)
        : provider.categories
            .where((cat) => cat.type == parseResult.transaction.type)
            .take(5)
            .map((cat) => cat.name)
            .toList()
          ..add('Other');

    // Create system message with smart category suggestions
    final quickReplyId = _uuid.v4();
    final message = ChatMessage.systemWithQuickReplies(
      id: _uuid.v4(),
      text: contextualMessage,
      timestamp: DateTime.now(),
      quickReplies: [...smartSuggestions, 'Cancel'],
      quickReplyId: quickReplyId,
    );

    await provider.addMessage(message);
    _scrollToBottom();
  }

  Future<void> _handleAmountConfirmation(ParseResult parseResult, String originalText) async {
    print('DEBUG: _handleAmountConfirmation called');
    print('DEBUG: parseResult.candidateTexts: ${parseResult.candidateTexts}');
    print('DEBUG: parseResult.candidateAmounts: ${parseResult.candidateAmounts}');

    // Null safety check for candidateTexts
    final candidateTexts = parseResult.candidateTexts;
    if (candidateTexts == null || candidateTexts.isEmpty) {
      print('ERROR: candidateTexts is null or empty');
      _addSystemMessage('Sorry, I encountered an error processing the amounts. Please try again.');
      return;
    }

    // Store pending transaction for amount confirmation
    _pendingAmountConfirmation = parseResult;
    _pendingOriginalText = originalText;

    print('DEBUG: Creating quick replies with candidates: $candidateTexts');

    // Generate contextual message
    final contextualMessage = _generateContextualMessage(parseResult, originalText);

    // Create system message with quick replies for amount selection
    final quickReplyId = _uuid.v4();
    final message = ChatMessage.systemWithQuickReplies(
      id: _uuid.v4(),
      text: contextualMessage,
      timestamp: DateTime.now(),
      quickReplies: [...candidateTexts, 'Cancel'],
      quickReplyId: quickReplyId,
    );

    final provider = Provider.of<TransactionProvider>(context, listen: false);
    await provider.addMessage(message);
    _scrollToBottom();

    print('DEBUG: Amount confirmation UI displayed successfully');
  }

  Future<void> _handleCategorySelection(ParseResult parseResult, String originalText) async {
    final provider = Provider.of<TransactionProvider>(context, listen: false);

    // Show enhanced category picker dialog with smart suggestions
    final selectedCategoryId = await showCategoryPickerDialog(
      context: context,
      transactionType: parseResult.transaction.type,
      initialCategoryId: parseResult.transaction.categoryId,
      transactionText: originalText,
      categoryFinderService: _parserService?.categoryFinder,
    );

    if (selectedCategoryId != null) {
      // Update transaction with selected category
      final updatedTransaction = parseResult.transaction.copyWith(
        categoryId: selectedCategoryId,
      );

      // Save the learning association (both type and category if available)
      if (_learnedAssociationService != null) {
        await _learnedAssociationService!.learn(
          originalText,
          type: updatedTransaction.type,
          categoryId: selectedCategoryId,
        );
      }

      // Save the transaction
      await provider.addTransactionFromChat(updatedTransaction, originalText);

      // Add learning confirmation message
      final category = provider.getCategoryById(selectedCategoryId);
      if (category != null) {
        // Extract key words from the original text for learning message
        final keywords = _extractKeywords(originalText);
        if (keywords.isNotEmpty) {
          _addSystemMessage(
            '💡 I\'ll remember to categorize "${keywords}" as ${category.name} for you next time.',
          );
        }
      }

      _scrollToBottom();
    } else {
      // User cancelled - save with original category
      await provider.addTransactionFromChat(parseResult.transaction, originalText);
      _scrollToBottom();
    }
  }

  void _addUserMessage(String text) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    final message = ChatMessage.user(
      id: _uuid.v4(),
      text: text,
      timestamp: DateTime.now(),
    );
    provider.addMessage(message);

    // Always scroll to bottom when a new message is added
    _scrollToBottom();
  }

  void _addSystemMessage(String text, {String? transactionId}) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    final message = ChatMessage.system(
      id: _uuid.v4(),
      text: text,
      timestamp: DateTime.now(),
      associatedTransactionId: transactionId,
    );
    provider.addMessage(message);

    // Always scroll to bottom when a new message is added
    _scrollToBottom();
  }

  // Handle quick reply selection
  Future<void> _onQuickReplySelected(String quickReplyId, String selectedOption) async {
    print('DEBUG: _onQuickReplySelected called with quickReplyId: "$quickReplyId", selectedOption: "$selectedOption"');
    print('DEBUG: _pendingTypeSelection: ${_pendingTypeSelection != null}');
    print('DEBUG: _pendingAmountConfirmation: ${_pendingAmountConfirmation != null}');

    if (_pendingTypeSelection != null && _pendingOriginalText != null) {
      print('DEBUG: Routing to type selection response');
      await _handleTypeSelectionResponse(selectedOption);
    } else if (_pendingCategorySelection != null && _pendingOriginalText != null) {
      print('DEBUG: Routing to category selection response');
      await _handleCategorySelectionResponse(selectedOption);
    } else if (_pendingAmountConfirmation != null && _pendingOriginalText != null) {
      print('DEBUG: Routing to amount confirmation response');
      await _handleAmountConfirmationResponse(selectedOption);
    } else {
      print('ERROR: No pending selection found for quick reply');
    }
  }

  // Handle transaction type selection response
  Future<void> _handleTypeSelectionResponse(String selectedType) async {
    if (_pendingTypeSelection == null || _pendingOriginalText == null) return;

    if (selectedType == 'Cancel') {
      // User cancelled
      _addSystemMessage('Transaction cancelled.');
      _pendingTypeSelection = null;
      _pendingOriginalText = null;
      return;
    }

    // Convert selected type to TransactionType enum
    TransactionType? transactionType;
    switch (selectedType) {
      case 'Expense':
        transactionType = TransactionType.expense;
        break;
      case 'Income':
        transactionType = TransactionType.income;
        break;
      default:
        transactionType = TransactionType.expense;
    }

    // Learn the type association
    if (_learnedAssociationService != null) {
      await _learnedAssociationService!.learn(_pendingOriginalText!, type: transactionType);
    }

    // Update the pending transaction with the selected type
    final updatedTransaction = _pendingTypeSelection!.transaction.copyWith(
      type: transactionType,
    );

    // Now proceed to category detection
    // For now, we'll assume category selection is needed and let the user choose
    final parseResult = ParseResult.needsCategory(updatedTransaction);
    await _handleCategorySelection(parseResult, _pendingOriginalText!);

    // Clear pending state
    _pendingTypeSelection = null;
    _pendingOriginalText = null;
  }

  // Handle category selection response
  Future<void> _handleCategorySelectionResponse(String selectedOption) async {
    if (_pendingCategorySelection == null || _pendingOriginalText == null) return;

    if (selectedOption == 'Cancel') {
      // User cancelled
      _addSystemMessage('Transaction cancelled.');
      _pendingCategorySelection = null;
      _pendingOriginalText = null;
      return;
    }

    final provider = Provider.of<TransactionProvider>(context, listen: false);

    if (selectedOption == 'Other') {
      // Show enhanced category picker dialog for more options
      final selectedCategoryId = await showCategoryPickerDialog(
        context: context,
        transactionType: _pendingCategorySelection!.transaction.type,
        initialCategoryId: _pendingCategorySelection!.transaction.categoryId,
        transactionText: _pendingOriginalText,
        categoryFinderService: _parserService?.categoryFinder,
      );

      if (selectedCategoryId != null) {
        await _processCategorySelection(selectedCategoryId);
      } else {
        // User cancelled dialog - save with original category
        await provider.addTransactionFromChat(_pendingCategorySelection!.transaction, _pendingOriginalText!);
        _scrollToBottom();
      }
    } else {
      // User selected a suggested category
      final selectedCategory = provider.categories.firstWhere(
        (cat) => cat.name == selectedOption,
        orElse: () => provider.categories.first,
      );

      await _processCategorySelection(selectedCategory.id);
    }

    // Clear pending state
    _pendingCategorySelection = null;
    _pendingOriginalText = null;
  }

  // Helper method to process category selection
  Future<void> _processCategorySelection(String selectedCategoryId) async {
    final provider = Provider.of<TransactionProvider>(context, listen: false);

    // Update transaction with selected category
    final updatedTransaction = _pendingCategorySelection!.transaction.copyWith(
      categoryId: selectedCategoryId,
    );

    // Save the learning association
    if (_learnedAssociationService != null) {
      await _learnedAssociationService!.learn(
        _pendingOriginalText!,
        type: updatedTransaction.type,
        categoryId: selectedCategoryId,
      );
    }

    // Save the transaction
    await provider.addTransactionFromChat(updatedTransaction, _pendingOriginalText!);

    // Add learning confirmation message
    final category = provider.getCategoryById(selectedCategoryId);
    if (category != null) {
      final keywords = _extractKeywords(_pendingOriginalText!);
      if (keywords.isNotEmpty) {
        _addSystemMessage(
          '💡 I\'ll remember to categorize "${keywords}" as ${category.name} for you next time.',
        );
      }
    }

    _scrollToBottom();
  }

  // Handle amount confirmation response
  Future<void> _handleAmountConfirmationResponse(String selectedOption) async {
    print('DEBUG: _handleAmountConfirmationResponse called with option: "$selectedOption"');

    if (_pendingAmountConfirmation == null || _pendingOriginalText == null) {
      print('ERROR: No pending amount confirmation found');
      return;
    }

    if (selectedOption == 'Cancel') {
      // User cancelled
      print('DEBUG: User cancelled amount confirmation');
      _addSystemMessage('Transaction cancelled.');
      _pendingAmountConfirmation = null;
      _pendingOriginalText = null;
      return;
    }

    // Find the selected amount from the candidate texts with null safety
    final candidateTexts = _pendingAmountConfirmation!.candidateTexts;
    final candidateAmounts = _pendingAmountConfirmation!.candidateAmounts;

    if (candidateTexts == null || candidateAmounts == null) {
      print('ERROR: candidateTexts or candidateAmounts is null');
      _addSystemMessage('Sorry, I encountered an error processing your selection. Please try again.');
      return;
    }

    print('DEBUG: Looking for "$selectedOption" in candidates: $candidateTexts');

    final selectedIndex = candidateTexts.indexOf(selectedOption);
    if (selectedIndex == -1) {
      print('ERROR: Selected option "$selectedOption" not found in candidates');
      _addSystemMessage('Invalid selection. Please try again.');
      return;
    }

    print('DEBUG: Found selected option at index $selectedIndex');
    final confirmedAmount = candidateAmounts[selectedIndex];
    print('DEBUG: User selected amount: $confirmedAmount');

    // Complete the transaction with the confirmed amount using the parser service
    if (_parserService != null) {
      try {
        final parseResult = await _parserService!.completeTransaction(_pendingOriginalText!, confirmedAmount);

        // Handle the result (might need category selection)
        switch (parseResult.status) {
          case ParseStatus.success:
            final provider = Provider.of<TransactionProvider>(context, listen: false);
            await provider.addTransactionFromChat(parseResult.transaction, _pendingOriginalText!);
            _addSystemMessage('✅ Transaction saved with amount ${_formatCurrency(confirmedAmount, parseResult.transaction.currencyCode)}');
            break;
          case ParseStatus.needsCategory:
            await _handleCategorySelection(parseResult, _pendingOriginalText!);
            break;
          default:
            _addSystemMessage('Error completing transaction. Please try again.');
        }
      } catch (e) {
        _addSystemMessage('Error processing transaction: $e');
      }
    }

    // Clear pending state
    _pendingAmountConfirmation = null;
    _pendingOriginalText = null;
  }

  // Helper method to format currency
  String _formatCurrency(double amount, String currencyCode) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    return provider.formatCurrency(amount, currencyCode);
  }

  // Helper method to extract keywords from text for learning messages
  String _extractKeywords(String text) {
    // Remove common words and extract meaningful keywords
    final words = text.toLowerCase().split(RegExp(r'\s+'));
    final stopWords = {'for', 'on', 'at', 'in', 'to', 'from', 'with', 'the', 'a', 'an', 'and', 'or', 'but'};
    final keywords = words.where((word) =>
      word.length > 2 &&
      !stopWords.contains(word) &&
      !RegExp(r'^\d+$').hasMatch(word) && // Not just numbers
      !RegExp(r'^[\$€£¥₹₽₩₱₫฿₺₪]+$').hasMatch(word) // Not just currency symbols
    ).take(3).join(' ');

    return keywords;
  }

  Future<void> _showEditDialog(Transaction transaction) async {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    
    final result = await showDialog<Transaction>(
      context: context,
      builder: (context) => TransactionEditDialog(transaction: transaction),
    );
    
    if (result != null) {
      // Update the transaction
      await provider.updateTransaction(result);
      
      // Show success message
      final category = provider.getCategoryById(result.categoryId);
      final categoryName = category?.name ?? 'Other';
      
      _addSystemMessage(
        '✅ Transaction updated: ${provider.formatCurrency(result.amount)} for ${result.description}, Category: $categoryName',
        transactionId: result.id,
      );
    }
  }

  Future<void> _showDeleteConfirmation(String transactionId) async {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    final transaction = provider.transactions.firstWhere((t) => t.id == transactionId);
    
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: Text(
          'Are you sure you want to delete this transaction: ${provider.formatCurrency(transaction.amount)} for ${transaction.description}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    
    if (result == true) {
      await provider.deleteTransaction(transactionId);
      
      _addSystemMessage(
        '🗑️ Transaction deleted successfully.',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Call super.build to properly handle keep alive
    super.build(context);
    
    final theme = Theme.of(context);
    final provider = Provider.of<TransactionProvider>(context);
    final messages = provider.messages;
    final isLoading = provider.isLoading;

    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.chat_bubble),
            SizedBox(width: 8),
            Text('Money Lover Chat'),
          ],
        ),
        scrolledUnderElevation: 3,
        shadowColor: theme.colorScheme.shadow.withOpacity(0.1),
      ),
      body: Column(
        children: [
          // Loading indicator for pagination
          if (isLoading && !_initialLoading)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8),
              color: theme.colorScheme.primaryContainer.withOpacity(0.3),
              child: const Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 16, 
                      height: 16, 
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    SizedBox(width: 8),
                    Text('Loading older messages...'),
                  ],
                ),
              ),
            ),
            
          // Messages list
          Expanded(
            child: _initialLoading
                ? _buildInitialLoadingState(theme)
                : (messages.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                        itemCount: messages.length,
                        itemBuilder: (context, index) {
                          final message = messages[index];
                          final isLastMessage = index == messages.length - 1;
                          
                          // For animation of the last message
                          if (isLastMessage) {
                            _animationController.forward(from: 0.0);
                          }
                          
                          return SlideTransition(
                            position: Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
                              CurvedAnimation(
                                parent: _animationController,
                                curve: Curves.easeOut,
                              ),
                            ),
                            child: FadeTransition(
                              opacity: _animationController,
                              child: _buildMessageBubble(message, theme),
                            ),
                          );
                        },
                      )
                ),
          ),
          
          // Message input
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.shadow.withOpacity(0.1),
                  blurRadius: 3,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
            child: SafeArea(
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      decoration: InputDecoration(
                        hintText: 'Type a transaction...',
                        prefixIcon: const Icon(Icons.text_fields),
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () => _messageController.clear(),
                          tooltip: 'Clear',
                        ),
                      ),
                      minLines: 1,
                      maxLines: 3,
                      textCapitalization: TextCapitalization.sentences,
                      onSubmitted: (_) => _sendMessage(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  FloatingActionButton(
                    onPressed: _sendMessage,
                    mini: true,
                    elevation: 0,
                    child: const Icon(Icons.send),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 80,
            color: Colors.grey.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Start a conversation',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'Try saying something like "Spent \$25 on dinner" or "Got \$1500 salary today"',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.withOpacity(0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialLoadingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 50,
            height: 50,
            child: CircularProgressIndicator(
              color: theme.colorScheme.primary,
              strokeWidth: 4,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Loading messages...',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please wait while we fetch your conversation history',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message, ThemeData theme) {
    final isUserMessage = message.type == ChatMessageType.user;
    final hasQuickReplies = message.type == ChatMessageType.systemWithQuickReplies;
    final provider = Provider.of<TransactionProvider>(context, listen: false);

    // Check if this message is associated with a transaction
    Transaction? associatedTransaction;
    if (message.associatedTransactionId != null) {
      try {
        associatedTransaction = provider.transactions.firstWhere(
          (t) => t.id == message.associatedTransactionId,
        );
      } catch (e) {
        // Transaction might have been deleted
        associatedTransaction = null;
      }
    }

    // Get the category if there's an associated transaction
    Category? category;
    if (associatedTransaction != null) {
      category = provider.getCategoryById(associatedTransaction.categoryId);
    }
    
    return Align(
      alignment: isUserMessage ? Alignment.centerRight : Alignment.centerLeft,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.8,
        ),
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 4),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isUserMessage 
                ? theme.brightness == Brightness.light 
                    ? theme.colorScheme.primary 
                    : theme.colorScheme.primary.withOpacity(0.8)
                : theme.brightness == Brightness.light 
                    ? Colors.grey.shade200 
                    : Colors.grey.shade800,
            borderRadius: BorderRadius.circular(18).copyWith(
              bottomRight: isUserMessage ? const Radius.circular(0) : null,
              bottomLeft: !isUserMessage ? const Radius.circular(0) : null,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Message text
              Text(
                message.text,
                style: TextStyle(
                  color: isUserMessage 
                      ? Colors.white 
                      : theme.brightness == Brightness.light 
                          ? Colors.black87 
                          : Colors.white,
                  fontSize: 16,
                ),
              ),
              
              // Transaction message if available
              if (associatedTransaction != null && category != null)
                TransactionMessage(
                  transaction: associatedTransaction,
                  category: category,
                  onEdit: _showEditDialog,
                  onDelete: _showDeleteConfirmation,
                ),

              // Quick replies if available
              if (hasQuickReplies && message.quickReplies != null && message.quickReplies!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: QuickReplyWidget(
                    replyOptions: message.quickReplies!,
                    onReplySelected: (selectedOption) {
                      _onQuickReplySelected(message.quickReplyId ?? '', selectedOption);
                    },
                  ),
                ),

              // Timestamp
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  _formatTime(message.timestamp),
                  style: TextStyle(
                    fontSize: 10,
                    color: isUserMessage
                        ? Colors.white.withOpacity(0.7)
                        : Colors.grey,
                  ),
                  textAlign: isUserMessage ? TextAlign.right : TextAlign.left,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final date = DateTime(dateTime.year, dateTime.month, dateTime.day);
    
    final hours = dateTime.hour.toString().padLeft(2, '0');
    final minutes = dateTime.minute.toString().padLeft(2, '0');
    final time = '$hours:$minutes';
    
    if (date == today) {
      return 'Today, $time';
    } else if (date == today.subtract(const Duration(days: 1))) {
      return 'Yesterday, $time';
    } else {
      final month = dateTime.month.toString().padLeft(2, '0');
      final day = dateTime.day.toString().padLeft(2, '0');
      return '$day/$month, $time';
    }
  }

  // Listen to transaction provider changes to scroll to bottom when messages are added
  void _onProviderChanged() {
    // Check if we need to scroll (only if we're near the bottom already)
    if (_scrollController.hasClients) {
      final position = _scrollController.position;
      final maxScroll = position.maxScrollExtent;
      final currentScroll = position.pixels;
      
      // If we're near the bottom (within 200 pixels), scroll all the way down
      if (maxScroll - currentScroll < 200) {
        _scrollToBottom();
      }
    }
  }
}